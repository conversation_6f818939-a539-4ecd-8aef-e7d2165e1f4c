'use client';

import React from 'react';
import classNames from 'classnames';

interface TranscriptPopperProps {
  isVisible: boolean;
  transcript: string;
  className?: string;
}

const TranscriptPopper: React.FC<TranscriptPopperProps> = ({
  isVisible,
  transcript,
  className
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={classNames(
        'absolute bottom-full mb-3 left-1/2 transform -translate-x-1/2 z-50',
        'bg-bg-box rounded-lg shadow-lg border border-color-border',
        'px-4 py-3 min-w-[300px] max-w-[400px]',
        'animate-in fade-in-0 zoom-in-95 duration-200',
        className
      )}
    >
      {/* Arrow pointing down */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
        <div className="w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-bg-box"></div>
        <div className="absolute -top-[1px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[9px] border-r-[9px] border-t-[9px] border-l-transparent border-r-transparent border-t-color-border"></div>
      </div>

      {/* Content */}
      <div className="text-sm text-color-major leading-relaxed max-h-[120px] overflow-y-auto">
        {transcript ? (
          <span>{transcript}</span>
        ) : (
          <span className="text-color-minor italic">Đang lắng nghe...</span>
        )}
      </div>
    </div>
  );
};

export default TranscriptPopper;
