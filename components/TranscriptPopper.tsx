'use client';

import React, { useMemo } from 'react';
import classNames from 'classnames';

interface TranscriptPopperProps {
  isVisible: boolean;
  transcript: string;
  className?: string;
  template?: string; // Template text để so sánh
}

interface ComparisonResult {
  highlightedTemplate: JSX.Element;
}

// Function để tính similarity giữa 2 từ (Levenshtein distance)
const calculateWordSimilarity = (word1: string, word2: string): number => {
  if (word1 === word2) return 1;
  if (word1.length === 0) return word2.length === 0 ? 1 : 0;
  if (word2.length === 0) return 0;

  const matrix = Array(word2.length + 1).fill(null).map(() => Array(word1.length + 1).fill(null));

  for (let i = 0; i <= word1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= word2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= word2.length; j++) {
    for (let i = 1; i <= word1.length; i++) {
      const indicator = word1[i - 1] === word2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  const distance = matrix[word2.length][word1.length];
  const maxLength = Math.max(word1.length, word2.length);
  return (maxLength - distance) / maxLength;
};

const TranscriptPopper: React.FC<TranscriptPopperProps> = ({
  isVisible,
  transcript,
  template,
  className
}) => {
  if (!isVisible) return null;

  // Function để so sánh transcript với template
  const compareTranscriptWithTemplate = useMemo((): ComparisonResult => {
    if (!template || !transcript) {
      return {
        highlightedTemplate: <span>{template || 'Không có template'}</span>
      };
    }



    // Tạo highlighted template với word-level comparison
    const words = template.split(' ');
    const transcriptWords = transcript.trim().split(' ');
    // Giới hạn transcriptWordCount không vượt quá số từ trong template
    const transcriptWordCount = Math.min(transcriptWords.length, words.length);

    const highlightedTemplate = (
      <span>
        {words.map((word, index) => {
          const isHighlighted = index < transcriptWordCount;
          let isMatched = false;

          if (isHighlighted && transcriptWords[index]) {
            // So sánh từng từ (normalize để so sánh chính xác hơn)
            const normalizedTemplateWord = word.toLowerCase().replace(/[^\w]/g, '');
            const normalizedTranscriptWord = transcriptWords[index].toLowerCase().replace(/[^\w]/g, '');

            // Tính similarity giữa 2 từ
            const similarity = calculateWordSimilarity(normalizedTemplateWord, normalizedTranscriptWord);
            isMatched = similarity > 0.7; // 70% similarity threshold
          }

          return (
            <span
              key={index}
              className={classNames({
                'font-bold': isHighlighted,
                'text-green-600 bg-green-50': isHighlighted && isMatched,
                'text-red-500 bg-red-50': isHighlighted && !isMatched,
                'text-color-minor': !isHighlighted,
              })}
            >
              {word}
              {index < words.length - 1 ? ' ' : ''}
            </span>
          );
        })}
      </span>
    );



    return {
      highlightedTemplate
    };
  }, [template, transcript]);

  const { highlightedTemplate } = compareTranscriptWithTemplate;

  return (
    <div
      className={classNames(
        'absolute bottom-full mb-3 left-1/2 transform -translate-x-1/2 z-50',
        'bg-bg-box rounded-lg shadow-lg border border-color-border',
        'px-5 py-4 min-w-[400px] max-w-[500px]',
        'animate-in fade-in-0 zoom-in-95 duration-200',
        className
      )}
    >
      {/* Arrow pointing down */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
        <div className="w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-bg-box"></div>
        <div className="absolute -top-[1px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[9px] border-r-[9px] border-t-[9px] border-l-transparent border-r-transparent border-t-color-border"></div>
      </div>

      {/* Content */}
      <div className="text-sm leading-relaxed">
        {template ? (
          <div className="text-color-major text-base leading-relaxed">
            {highlightedTemplate}
          </div>
        ) : (
          // Fallback khi không có template
          <div className="text-color-major">
            {transcript ? (
              <span>{transcript}</span>
            ) : (
              <span className="text-color-minor italic">Đang lắng nghe...</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptPopper;
